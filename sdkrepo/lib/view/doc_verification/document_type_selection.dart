import 'package:example/view/common/InformationalScreen.dart';
import 'package:flutter/cupertino.dart';
import '../../resources/exports/index.dart';

enum EnumDocumentTypeSelection { non, id, passport }

class DocumentTypeSelectionWidget extends StatefulWidget {
  final Function(DocumentTypeSelection) onDocumentTypeSelected;

  const DocumentTypeSelectionWidget({
    super.key,
    required this.onDocumentTypeSelected,
  });

  @override
  State<DocumentTypeSelectionWidget> createState() => _DocumentTypeSelectionWidgetState();
}

class _DocumentTypeSelectionWidgetState extends State<DocumentTypeSelectionWidget> {
  EnumDocumentTypeSelection _selectedDocumentType = EnumDocumentTypeSelection.non;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: "Document Verification",
        backallow: true,
      ),
      backgroundColor: AppColors.white,
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: <PERSON>um<PERSON>(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Spacer(),
            SvgPicture.asset("assets/images/DocumentScan2.svg"),
            const SpaceH20(),
            // Title
            const Text(
              "Please choose a document to scan",
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.w700,
              ),
            ),

            const Spacer(),
            // National ID Button
            _buildDocumentTypeButton(
              context: context,
              title: "National ID",
              documentType: EnumDocumentTypeSelection.id,
              onPressed: () => _selectDocumentType(EnumDocumentTypeSelection.id),
            ),
            const SpaceH16(),
            // Passport Button
            _buildDocumentTypeButton(
              context: context,
              title: "Passport",
              documentType: EnumDocumentTypeSelection.passport,
              onPressed: () => _selectDocumentType(EnumDocumentTypeSelection.passport),
            ),

            const Spacer(),
            // Proceed Button
            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: _selectedDocumentType != EnumDocumentTypeSelection.non
                  ? () {
                      if (_selectedDocumentType == EnumDocumentTypeSelection.id) {
                        Get.to(Informationalscreen(imageOption: InformationalImageOptions.scanDocument, title: "Scan Document Front", onSubmit: () {}));
                      } else if (_selectedDocumentType == EnumDocumentTypeSelection.passport) {
                        Get.to(Informationalscreen(imageOption: InformationalImageOptions.scanDocument, title: "Scan Passport Main Page", onSubmit: () {}));
                      }
                    }
                  : null,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 14),
                width: double.maxFinite,
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  color: _selectedDocumentType != EnumDocumentTypeSelection.non ? const Color(0xff8240DE) : const Color(0xff8240DE).withOpacity(0.5),
                  shape: ContinuousRectangleBorder(
                    borderRadius: BorderRadius.circular(24.0),
                  ),
                ),
                alignment: Alignment.center,
                child: DefaultTextStyle(
                  style: TextStyle(
                    color: _selectedDocumentType != EnumDocumentTypeSelection.non ? Colors.white : Colors.white.withOpacity(0.7),
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  child: const Text("Proceed"),
                ),
              ),
            ),

            const Spacer(),
          ],
        ),
      ),
    );
  }

  void _selectDocumentType(EnumDocumentTypeSelection documentType) {
    setState(() {
      _selectedDocumentType = documentType;
    });
  }

  Widget _buildDocumentTypeButton({
    required BuildContext context,
    required String title,
    required EnumDocumentTypeSelection documentType,
    required VoidCallback onPressed,
  }) {
    bool isSelected = _selectedDocumentType == documentType;

    return CupertinoButton(
      onPressed: onPressed,
      padding: EdgeInsets.zero,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: ShapeDecoration(
          color: isSelected ? const Color(0xff8240DE).withOpacity(0.1) : const Color(0xffE8E0EB),
          shape: ContinuousRectangleBorder(
            borderRadius: BorderRadius.circular(24.0),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Radio<EnumDocumentTypeSelection>(
                value: documentType,
                groupValue: _selectedDocumentType,
                onChanged: (EnumDocumentTypeSelection? value) {
                  if (value != null) {
                    _selectDocumentType(value);
                  }
                },
                activeColor: const Color(0xff8240DE),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: context.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                  color: isSelected ? const Color(0xff8240DE) : Colors.black,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
